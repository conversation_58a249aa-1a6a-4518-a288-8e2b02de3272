"use client"

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Clock, Users, Zap, Heart, Star, ArrowRight } from 'lucide-react'

export default function GenyusSalesPage() {
  const [isYearly, setIsYearly] = useState(false)

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Hero Section */}
      <div className="relative overflow-hidden">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-16">
          <div className="text-center">
            <div className="inline-flex items-center px-4 py-2 bg-blue-100 rounded-full text-blue-800 text-sm font-medium mb-8">
              <Brain className="w-4 h-4 mr-2" />
              Revolutionary AI Memory Technology
            </div>
            
            <h1 className="text-5xl md:text-7xl font-bold text-gray-900 mb-6">
              The AI That
              <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent"> Never Forgets</span>
            </h1>
            
            <p className="text-xl md:text-2xl text-gray-600 mb-8 max-w-4xl mx-auto leading-relaxed">
              Meet OnlyGenyus - the first AI companion that builds a genuine relationship with you. 
              It remembers your projects from 8 months ago, learns your communication style, 
              and grows more helpful with every conversation.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
              <Button size="lg" className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 text-lg">
                Start Free with 20,000 Words
                <ArrowRight className="ml-2 w-5 h-5" />
              </Button>
              <Button variant="outline" size="lg" className="px-8 py-4 text-lg">
                Watch Demo
              </Button>
            </div>

            <div className="text-sm text-gray-500">
              ✨ 20,000 words free your first month • No credit card required • Upgrade anytime
            </div>
          </div>
        </div>
      </div>

      {/* Memory Showcase */}
      <div className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Memory That Actually Works
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Unlike other AI chatbots that forget everything, OnlyGenyus builds a comprehensive memory 
              of your conversations, preferences, and goals.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 mb-16">
            <div className="bg-gradient-to-br from-blue-50 to-blue-100 p-8 rounded-2xl">
              <Clock className="w-12 h-12 text-blue-600 mb-4" />
              <h3 className="text-xl font-semibold mb-3">Remembers Everything</h3>
              <p className="text-gray-600">
                "Remember that Python project we discussed 8 months ago?" 
                OnlyGenyus will instantly recall the details, your progress, and next steps.
              </p>
            </div>

            <div className="bg-gradient-to-br from-purple-50 to-purple-100 p-8 rounded-2xl">
              <Heart className="w-12 h-12 text-purple-600 mb-4" />
              <h3 className="text-xl font-semibold mb-3">Builds Relationships</h3>
              <p className="text-gray-600">
                Learns your communication style, celebrates your wins, supports your challenges. 
                It's like having a brilliant friend who truly knows you.
              </p>
            </div>

            <div className="bg-gradient-to-br from-green-50 to-green-100 p-8 rounded-2xl">
              <Zap className="w-12 h-12 text-green-600 mb-4" />
              <h3 className="text-xl font-semibold mb-3">Gets Smarter</h3>
              <p className="text-gray-600">
                Every conversation makes it better. It learns what works for you, 
                adapts to your preferences, and provides increasingly personalized help.
              </p>
            </div>
          </div>

          {/* Conversation Example */}
          <div className="bg-gray-50 rounded-2xl p-8">
            <h3 className="text-2xl font-semibold mb-6 text-center">See the Difference</h3>
            
            <div className="grid md:grid-cols-2 gap-8">
              {/* Other AI */}
              <div className="bg-white rounded-xl p-6 border-2 border-red-200">
                <div className="text-red-600 font-semibold mb-4 flex items-center">
                  <div className="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
                  Other AI Chatbots
                </div>
                <div className="space-y-3">
                  <div className="bg-blue-100 p-3 rounded-lg">
                    <strong>You:</strong> "Remember that marketing strategy we worked on?"
                  </div>
                  <div className="bg-gray-100 p-3 rounded-lg">
                    <strong>AI:</strong> "I don't have access to previous conversations. Could you provide more details about the marketing strategy you're referring to?"
                  </div>
                </div>
              </div>

              {/* OnlyGenyus */}
              <div className="bg-white rounded-xl p-6 border-2 border-green-200">
                <div className="text-green-600 font-semibold mb-4 flex items-center">
                  <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                  OnlyGenyus
                </div>
                <div className="space-y-3">
                  <div className="bg-blue-100 p-3 rounded-lg">
                    <strong>You:</strong> "Remember that marketing strategy we worked on?"
                  </div>
                  <div className="bg-green-100 p-3 rounded-lg">
                    <strong>OnlyGenyus:</strong> "Absolutely! You mean the content marketing strategy for your SaaS product from March. We focused on SEO-driven blog posts, LinkedIn thought leadership, and that email nurture sequence. How did the implementation go? Ready to optimize based on the results?"
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Features Grid */}
      <div className="py-20 bg-gradient-to-br from-gray-50 to-blue-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Why OnlyGenyus Changes Everything
            </h2>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: Brain,
                title: "Hierarchical Memory System",
                description: "Stores immediate conversations, compressed summaries, long-term insights, and permanent context about you."
              },
              {
                icon: Users,
                title: "Adaptive Communication",
                description: "Learns your vocabulary level, formality preference, and communication style. Matches your energy while enhancing it."
              },
              {
                icon: Star,
                title: "Relationship Milestones",
                description: "Tracks your growth, celebrates achievements, and builds genuine partnership over time."
              },
              {
                icon: Zap,
                title: "Contextual Intelligence",
                description: "Analyzes each question to surface the most relevant memories. Quality over quantity."
              },
              {
                icon: Heart,
                title: "Emotional Continuity",
                description: "Remembers your challenges, supports your goals, and maintains emotional context across conversations."
              },
              {
                icon: Clock,
                title: "Unlimited Timeline",
                description: "Never loses important information. Your AI partner grows more valuable over months and years."
              }
            ].map((feature, index) => (
              <div key={index} className="bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-shadow">
                <feature.icon className="w-10 h-10 text-blue-600 mb-4" />
                <h3 className="text-lg font-semibold mb-2">{feature.title}</h3>
                <p className="text-gray-600">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Pricing */}
      <div className="py-20 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Simple, Honest Pricing
            </h2>
            <p className="text-xl text-gray-600">
              Start free, upgrade when you're ready for unlimited conversations
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8 max-w-3xl mx-auto">
            {/* Free Tier */}
            <div className="bg-gray-50 p-8 rounded-2xl border-2 border-gray-200">
              <div className="text-center">
                <h3 className="text-2xl font-bold text-gray-900 mb-2">Free Forever</h3>
                <div className="text-4xl font-bold text-gray-900 mb-4">$0</div>
                <p className="text-gray-600 mb-6">Perfect for trying OnlyGenyus</p>
                
                <ul className="space-y-3 mb-8 text-left">
                  <li className="flex items-center">
                    <CheckCircle className="w-5 h-5 text-green-500 mr-3" />
                    20,000 words first month
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="w-5 h-5 text-green-500 mr-3" />
                    5,000 words every month after
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="w-5 h-5 text-green-500 mr-3" />
                    Full memory system
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="w-5 h-5 text-green-500 mr-3" />
                    Relationship building
                  </li>
                </ul>

                <Button variant="outline" className="w-full">
                  Start Free
                </Button>
              </div>
            </div>

            {/* Unlimited Tier */}
            <div className="bg-gradient-to-br from-blue-600 to-purple-600 p-8 rounded-2xl text-white relative">
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                <span className="bg-yellow-400 text-yellow-900 px-4 py-1 rounded-full text-sm font-semibold">
                  Most Popular
                </span>
              </div>
              
              <div className="text-center">
                <h3 className="text-2xl font-bold mb-2">Unlimited</h3>
                <div className="text-4xl font-bold mb-4">$9.99</div>
                <p className="text-blue-100 mb-6">For power users and professionals</p>
                
                <ul className="space-y-3 mb-8 text-left">
                  <li className="flex items-center">
                    <CheckCircle className="w-5 h-5 text-green-400 mr-3" />
                    Unlimited conversations
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="w-5 h-5 text-green-400 mr-3" />
                    Priority response speed
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="w-5 h-5 text-green-400 mr-3" />
                    Enhanced memory features
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="w-5 h-5 text-green-400 mr-3" />
                    Advanced relationship insights
                  </li>
                </ul>

                <Button className="w-full bg-white text-blue-600 hover:bg-gray-100">
                  Upgrade to Unlimited
                </Button>
              </div>
            </div>
          </div>

          <div className="text-center mt-8 text-gray-500">
            Cancel anytime • No hidden fees • 30-day money-back guarantee
          </div>
        </div>
      </div>

      {/* Social Proof */}
      <div className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Finally, an AI That Gets You
            </h2>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {[
              {
                quote: "I've been using OnlyGenyus for 6 months. It remembers every project detail, my communication style, even my goals. It's like having a brilliant assistant who actually knows me.",
                author: "Sarah Chen",
                role: "Product Manager"
              },
              {
                quote: "Other AI tools forget everything between conversations. OnlyGenyus builds on our previous discussions and gets more helpful over time. Game changer for my consulting work.",
                author: "Marcus Rodriguez", 
                role: "Business Consultant"
              },
              {
                quote: "The memory system is incredible. It referenced a coding problem we discussed 4 months ago and helped me apply those lessons to a new project. This is the future of AI.",
                author: "Alex Kim",
                role: "Software Developer"
              }
            ].map((testimonial, index) => (
              <div key={index} className="bg-white p-6 rounded-xl shadow-sm">
                <div className="flex mb-4">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                  ))}
                </div>
                <p className="text-gray-600 mb-4 italic">"{testimonial.quote}"</p>
                <div>
                  <div className="font-semibold text-gray-900">{testimonial.author}</div>
                  <div className="text-gray-500 text-sm">{testimonial.role}</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* CTA */}
      <div className="py-20 bg-gradient-to-r from-blue-600 to-purple-600">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl font-bold text-white mb-4">
            Ready for an AI That Actually Remembers?
          </h2>
          <p className="text-xl text-blue-100 mb-8">
            Start with 20,000 free words. No credit card required.
          </p>
          
          <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-100 px-8 py-4 text-lg">
            Start Your Free Trial
            <ArrowRight className="ml-2 w-5 h-5" />
          </Button>
          
          <div className="mt-6 text-blue-100 text-sm">
            Join thousands building lasting relationships with AI
          </div>
        </div>
      </div>
    </div>
  )
}
