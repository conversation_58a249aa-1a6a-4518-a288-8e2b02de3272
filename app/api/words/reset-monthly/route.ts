import { NextRequest } from "next/server";
import { createClient } from "@supabase/supabase-js";

export const runtime = "edge";

function createSbEdge() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    { auth: { persistSession: false } }
  );
}

export async function GET(req: NextRequest) {
  try {
    // Verify this is a cron request (optional security check)
    const authHeader = req.headers.get('authorization');
    if (authHeader !== `Bearer ${process.env.CRON_SECRET}`) {
      return new Response("Unauthorized", { status: 401 });
    }

    const supabase = createSbEdge();

    // Call the reset function
    const { data, error } = await supabase.rpc('genyus_reset_monthly_words');

    if (error) {
      console.error('Monthly reset error:', error);
      return new Response(JSON.stringify({ 
        error: "Reset failed", 
        details: error.message 
      }), { 
        status: 500,
        headers: { "Content-Type": "application/json" }
      });
    }

    console.log(`Monthly reset completed. ${data} users reset.`);

    return new Response(JSON.stringify({ 
      success: true, 
      usersReset: data,
      timestamp: new Date().toISOString()
    }), {
      status: 200,
      headers: { "Content-Type": "application/json" }
    });

  } catch (error) {
    console.error('Monthly reset error:', error);
    return new Response(JSON.stringify({ 
      error: "Internal server error" 
    }), { 
      status: 500,
      headers: { "Content-Type": "application/json" }
    });
  }
}
