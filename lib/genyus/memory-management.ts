import { createClient } from "@supabase/supabase-js";

function createSupabaseServiceClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        persistSession: false,
        autoRefreshToken: false
      }
    }
  );
}

// Hierarchical Memory Architecture
// Inspired by human memory: Working Memory → Short-term → Long-term → Permanent

export interface MemoryLayer {
  immediate: ConversationMemory[];     // Last 5-10 exchanges (always included)
  shortTerm: ConversationSummary[];    // Last 30 days, compressed
  longTerm: MemoryInsight[];           // Key patterns, preferences, important events
  permanent: CoreMemory;               // Essential user context that never fades
}

export interface ConversationMemory {
  id: string;
  timestamp: Date;
  userMessage: string;
  assistantResponse: string;
  importance: number; // 1-10 scale
  topics: string[];
  emotional_context: string;
  context_tags: string[];
}

export interface ConversationSummary {
  id: string;
  timeframe: string; // "Week of Jan 15-21, 2025"
  topic_focus: string[];
  key_insights: string[];
  relationship_progress: string;
  important_mentions: string[];
  compressed_content: string; // AI-generated summary
  original_message_count: number;
}

export interface MemoryInsight {
  type: 'preference' | 'goal' | 'expertise' | 'challenge' | 'relationship' | 'pattern';
  content: string;
  confidence: number; // 0-1
  supporting_evidence: string[];
  first_observed: Date;
  last_confirmed: Date;
  importance: number; // 1-10
}

export interface CoreMemory {
  user_id: string;
  essential_context: {
    communication_style: any;
    core_interests: string[];
    primary_goals: string[];
    relationship_stage: string;
    trust_level: number;
    key_preferences: Record<string, any>;
  };
  never_forget: string[]; // Critical things user explicitly asked to remember
  relationship_milestones: Array<{
    date: Date;
    milestone: string;
    significance: string;
  }>;
}

export class IntelligentMemoryManager {
  private supabase = createSupabaseServiceClient();

  // Smart memory retrieval based on current context
  async getContextualMemory(
    userId: string, 
    currentQuestion: string,
    maxTokens: number = 4000
  ): Promise<MemoryLayer> {
    
    // 1. Always include immediate memory (recent exchanges)
    const immediate = await this.getImmediateMemory(userId, 8);
    
    // 2. Analyze current question to determine relevant context
    const contextAnalysis = this.analyzeQuestionContext(currentQuestion);
    
    // 3. Retrieve relevant short-term memories
    const shortTerm = await this.getRelevantShortTermMemory(
      userId, 
      contextAnalysis.topics,
      contextAnalysis.timeReferences
    );
    
    // 4. Get applicable long-term insights
    const longTerm = await this.getRelevantLongTermInsights(
      userId,
      contextAnalysis.topics,
      contextAnalysis.intentType
    );
    
    // 5. Always include permanent memory
    const permanent = await this.getPermanentMemory(userId);
    
    // 6. Optimize for token limit while preserving most important context
    return this.optimizeMemoryForTokens({
      immediate,
      shortTerm,
      longTerm,
      permanent
    }, maxTokens);
  }

  // Compress older conversations into summaries
  async compressOldConversations(userId: string): Promise<void> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - 7); // Compress conversations older than 7 days
    
    // Get conversations to compress
    const { data: oldConversations } = await this.supabase
      .from('genyus_requests')
      .select(`
        id, question, created_at,
        genyus_answers!inner(final_text),
        genyus_conversation_context(mentioned_topics, emotional_tone, complexity_level)
      `)
      .eq('user_id', userId)
      .lt('created_at', cutoffDate.toISOString())
      .order('created_at', { ascending: true });

    if (!oldConversations || oldConversations.length === 0) return;

    // Group by week for compression
    const weeklyGroups = this.groupConversationsByWeek(oldConversations);
    
    for (const [weekKey, conversations] of Object.entries(weeklyGroups)) {
      await this.createWeeklySummary(userId, weekKey, conversations);
    }
    
    // Archive original conversations (don't delete, just mark as archived)
    const conversationIds = oldConversations.map(c => c.id);
    await this.supabase
      .from('genyus_requests')
      .update({ archived: true })
      .in('id', conversationIds);
  }

  // Extract and store long-term insights
  async extractLongTermInsights(userId: string): Promise<void> {
    // Analyze patterns across all user conversations
    const { data: allConversations } = await this.supabase
      .from('genyus_requests')
      .select(`
        question, created_at,
        genyus_answers(final_text),
        genyus_conversation_context(mentioned_topics, emotional_tone, message_type)
      `)
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(100); // Analyze last 100 conversations

    if (!allConversations) return;

    // Extract insights using AI analysis
    const insights = await this.analyzeConversationPatterns(allConversations);
    
    // Store insights
    for (const insight of insights) {
      await this.storeMemoryInsight(userId, insight);
    }
  }

  private async getImmediateMemory(userId: string, limit: number = 100): Promise<ConversationMemory[]> {
    // Store last 100 conversations in immediate memory - database cost is negligible
    // compared to revenue, and provides much richer context
    const { data } = await this.supabase
      .from('genyus_requests')
      .select(`
        id, question, created_at,
        genyus_answers!inner(final_text),
        genyus_conversation_context(mentioned_topics, emotional_tone)
      `)
      .eq('user_id', userId)
      .eq('archived', false)
      .order('created_at', { ascending: false })
      .limit(limit);

    return (data || []).map(conv => ({
      id: conv.id,
      timestamp: new Date(conv.created_at),
      userMessage: conv.question,
      assistantResponse: conv.genyus_answers[0]?.final_text || '',
      importance: this.calculateImportance(conv),
      topics: conv.genyus_conversation_context?.[0]?.mentioned_topics || [],
      emotional_context: conv.genyus_conversation_context?.[0]?.emotional_tone || '',
      context_tags: []
    }));
  }

  private async getRelevantShortTermMemory(
    userId: string, 
    topics: string[], 
    timeRefs: string[]
  ): Promise<ConversationSummary[]> {
    // Get summaries that match current topics or time references
    const { data } = await this.supabase
      .from('genyus_conversation_summaries')
      .select('*')
      .eq('user_id', userId)
      .or(`topic_focus.cs.{${topics.join(',')}},important_mentions.cs.{${timeRefs.join(',')}}`)
      .order('created_at', { ascending: false })
      .limit(5);

    return data || [];
  }

  private async getRelevantLongTermInsights(
    userId: string,
    topics: string[],
    intentType: string
  ): Promise<MemoryInsight[]> {
    // Get insights relevant to current context
    const { data } = await this.supabase
      .from('genyus_memory_insights')
      .select('*')
      .eq('user_id', userId)
      .eq('is_active', true)
      .gte('confidence_score', 0.7) // Only high-confidence insights
      .order('importance', { ascending: false })
      .limit(10);

    return (data || []).map(insight => ({
      type: insight.insight_type as any,
      content: insight.insight_data.content || '',
      confidence: insight.confidence_score,
      supporting_evidence: insight.supporting_messages || [],
      first_observed: new Date(insight.first_observed),
      last_confirmed: new Date(insight.last_confirmed),
      importance: insight.insight_data.importance || 5
    }));
  }

  private async getPermanentMemory(userId: string): Promise<CoreMemory> {
    const { data } = await this.supabase
      .from('genyus_user_memory')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (!data) {
      return {
        user_id: userId,
        essential_context: {
          communication_style: {},
          core_interests: [],
          primary_goals: [],
          relationship_stage: 'new',
          trust_level: 1,
          key_preferences: {}
        },
        never_forget: [],
        relationship_milestones: []
      };
    }

    return {
      user_id: userId,
      essential_context: {
        communication_style: data.communication_style || {},
        core_interests: data.interests || [],
        primary_goals: data.goals || [],
        relationship_stage: data.relationship_stage || 'new',
        trust_level: data.trust_level || 1,
        key_preferences: data.communication_adaptations || {}
      },
      never_forget: data.follow_up_items || [],
      relationship_milestones: [] // TODO: Extract from conversation history
    };
  }

  private optimizeMemoryForTokens(memory: MemoryLayer, maxTokens: number): MemoryLayer {
    // Estimate tokens (rough: 1 token ≈ 4 characters)
    const estimateTokens = (text: string) => Math.ceil(text.length / 4);
    
    let currentTokens = 0;
    
    // Always include permanent memory (essential)
    currentTokens += estimateTokens(JSON.stringify(memory.permanent));
    
    // Include immediate memory (most important)
    const optimizedImmediate = memory.immediate.slice(0, 6); // Limit to 6 most recent
    currentTokens += estimateTokens(JSON.stringify(optimizedImmediate));
    
    // Add long-term insights by importance
    const sortedInsights = memory.longTerm.sort((a, b) => b.importance - a.importance);
    const optimizedLongTerm = [];
    for (const insight of sortedInsights) {
      const insightTokens = estimateTokens(JSON.stringify(insight));
      if (currentTokens + insightTokens < maxTokens * 0.8) { // Reserve 20% for short-term
        optimizedLongTerm.push(insight);
        currentTokens += insightTokens;
      }
    }
    
    // Add short-term summaries if space allows
    const optimizedShortTerm = [];
    for (const summary of memory.shortTerm) {
      const summaryTokens = estimateTokens(JSON.stringify(summary));
      if (currentTokens + summaryTokens < maxTokens) {
        optimizedShortTerm.push(summary);
        currentTokens += summaryTokens;
      }
    }
    
    return {
      immediate: optimizedImmediate,
      shortTerm: optimizedShortTerm,
      longTerm: optimizedLongTerm,
      permanent: memory.permanent
    };
  }

  private analyzeQuestionContext(question: string) {
    const lowerQuestion = question.toLowerCase();
    
    // Extract topics
    const topics = [];
    const topicKeywords = ['work', 'business', 'health', 'family', 'technology', 'learning', 'creative', 'finance'];
    topicKeywords.forEach(topic => {
      if (lowerQuestion.includes(topic)) topics.push(topic);
    });
    
    // Extract time references
    const timeReferences = [];
    const timeKeywords = ['yesterday', 'last week', 'last month', 'before', 'previously', 'earlier'];
    timeKeywords.forEach(time => {
      if (lowerQuestion.includes(time)) timeReferences.push(time);
    });
    
    // Determine intent type
    let intentType = 'general';
    if (lowerQuestion.includes('remember') || lowerQuestion.includes('mentioned')) {
      intentType = 'recall';
    } else if (lowerQuestion.includes('continue') || lowerQuestion.includes('follow up')) {
      intentType = 'continuation';
    } else if (lowerQuestion.includes('help') || lowerQuestion.includes('advice')) {
      intentType = 'assistance';
    }
    
    return { topics, timeReferences, intentType };
  }

  private calculateImportance(conversation: any): number {
    let importance = 5; // Base importance
    
    // Increase importance for emotional content
    const emotionalTone = conversation.genyus_conversation_context?.[0]?.emotional_tone;
    if (emotionalTone && ['excited', 'frustrated', 'grateful'].includes(emotionalTone)) {
      importance += 2;
    }
    
    // Increase for longer conversations
    if (conversation.question.length > 200) importance += 1;
    
    // Increase for follow-up requests
    if (conversation.question.toLowerCase().includes('follow up')) importance += 2;
    
    return Math.min(10, importance);
  }

  private groupConversationsByWeek(conversations: any[]): Record<string, any[]> {
    const groups: Record<string, any[]> = {};
    
    conversations.forEach(conv => {
      const date = new Date(conv.created_at);
      const weekStart = new Date(date);
      weekStart.setDate(date.getDate() - date.getDay()); // Start of week
      const weekKey = weekStart.toISOString().split('T')[0];
      
      if (!groups[weekKey]) groups[weekKey] = [];
      groups[weekKey].push(conv);
    });
    
    return groups;
  }

  private async createWeeklySummary(userId: string, weekKey: string, conversations: any[]): Promise<void> {
    // AI-generated summary of the week's conversations
    const topics = new Set<string>();
    const insights: string[] = [];
    
    conversations.forEach(conv => {
      const context = conv.genyus_conversation_context?.[0];
      if (context?.mentioned_topics) {
        context.mentioned_topics.forEach((topic: string) => topics.add(topic));
      }
    });
    
    const summary = {
      user_id: userId,
      timeframe: `Week of ${weekKey}`,
      topic_focus: Array.from(topics),
      key_insights: insights,
      relationship_progress: 'Continued building rapport',
      important_mentions: [],
      compressed_content: `Week summary: ${conversations.length} conversations covering ${Array.from(topics).join(', ')}`,
      original_message_count: conversations.length
    };
    
    await this.supabase
      .from('genyus_conversation_summaries')
      .insert(summary);
  }

  private async analyzeConversationPatterns(conversations: any[]): Promise<MemoryInsight[]> {
    // This would use AI to analyze patterns
    // For now, return basic pattern detection
    const insights: MemoryInsight[] = [];
    
    // Detect recurring topics
    const topicCounts: Record<string, number> = {};
    conversations.forEach(conv => {
      const topics = conv.genyus_conversation_context?.[0]?.mentioned_topics || [];
      topics.forEach((topic: string) => {
        topicCounts[topic] = (topicCounts[topic] || 0) + 1;
      });
    });
    
    // Create insights for frequently discussed topics
    Object.entries(topicCounts).forEach(([topic, count]) => {
      if (count >= 3) {
        insights.push({
          type: 'pattern',
          content: `User frequently discusses ${topic}`,
          confidence: Math.min(1, count / 10),
          supporting_evidence: [],
          first_observed: new Date(),
          last_confirmed: new Date(),
          importance: Math.min(10, count)
        });
      }
    });
    
    return insights;
  }

  private async storeMemoryInsight(userId: string, insight: MemoryInsight): Promise<void> {
    await this.supabase
      .from('genyus_memory_insights')
      .upsert({
        user_id: userId,
        insight_type: insight.type,
        insight_data: { content: insight.content, importance: insight.importance },
        confidence_score: insight.confidence,
        supporting_messages: insight.supporting_evidence,
        first_observed: insight.first_observed.toISOString(),
        last_confirmed: insight.last_confirmed.toISOString(),
        is_active: true
      });
  }
}

export const memoryManager = new IntelligentMemoryManager();
